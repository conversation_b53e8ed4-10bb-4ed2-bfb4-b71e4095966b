import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Server, Database, Wifi, HardDrive, Cpu, Activity } from 'lucide-react';

interface StatusCardProps {
  title: string;
  value: string | number;
  unit?: string;
  status: 'online' | 'warning' | 'error';
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'stable';
}

const StatusCard: React.FC<StatusCardProps> = ({ 
  title, 
  value, 
  unit, 
  status, 
  icon, 
  trend = 'stable' 
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'online': return 'border-cyber-green text-cyber-green shadow-glow-green';
      case 'warning': return 'border-yellow-400 text-yellow-400 shadow-glow-yellow';
      case 'error': return 'border-red-400 text-red-400 shadow-glow-red';
      default: return 'border-gray-400 text-gray-400';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '↗';
      case 'down': return '↘';
      default: return '→';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.05 }}
      className={`bg-dark-card/70 backdrop-blur-sm border rounded-lg p-4 ${getStatusColor()} 
                  transition-all duration-300 hover:shadow-lg`}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-sm font-medium">{title}</span>
        </div>
        <span className="text-xs opacity-70">{getTrendIcon()}</span>
      </div>
      <div className="flex items-baseline space-x-1">
        <span className="text-2xl font-bold">{value}</span>
        {unit && <span className="text-sm opacity-70">{unit}</span>}
      </div>
    </motion.div>
  );
};

interface ServerListItemProps {
  name: string;
  ip: string;
  cpu: number;
  memory: number;
  status: 'online' | 'warning' | 'error';
  lastUpdate: string;
}

const ServerListItem: React.FC<ServerListItemProps> = ({
  name,
  ip,
  cpu,
  memory,
  status,
  lastUpdate
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'online': return 'bg-cyber-green';
      case 'warning': return 'bg-yellow-400';
      case 'error': return 'bg-red-400';
      default: return 'bg-gray-400';
    }
  };



  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="bg-dark-card/50 border border-gray-600/30 rounded-lg p-3 mb-2 
                 hover:border-cyber-blue/50 transition-all duration-300"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()} animate-pulse`} />
          <div>
            <div className="font-medium text-white">{name}</div>
            <div className="text-xs text-gray-400">{ip}</div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-400">CPU: {cpu}% | 内存: {memory}%</div>
          <div className="text-xs text-gray-500">{lastUpdate}</div>
        </div>
      </div>
    </motion.div>
  );
};

const StatusIndicators: React.FC = () => {
  const [stats, setStats] = useState({
    totalServers: 71,
    onlineServers: 68,
    cpuUsage: 45,
    memoryUsage: 67,
    networkTraffic: 234,
    diskUsage: 78
  });

  const [servers] = useState([
    { name: '通用服务器CPU使用率>90%', ip: '*************', cpu: 92, memory: 78, status: 'error' as const, lastUpdate: '2020-12-01 12:23:34' },
    { name: '通用服务器CPU使用率>90%', ip: '*************', cpu: 94, memory: 82, status: 'error' as const, lastUpdate: '2020-12-01 12:23:34' },
    { name: '通用服务器CPU使用率>90%', ip: '*************', cpu: 88, memory: 75, status: 'warning' as const, lastUpdate: '2020-12-01 12:23:34' },
    { name: '通用服务器CPU使用率>90%', ip: '*************', cpu: 91, memory: 79, status: 'error' as const, lastUpdate: '2020-12-01 12:23:34' },
  ]);

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        cpuUsage: Math.max(20, Math.min(95, prev.cpuUsage + (Math.random() - 0.5) * 10)),
        memoryUsage: Math.max(30, Math.min(90, prev.memoryUsage + (Math.random() - 0.5) * 8)),
        networkTraffic: Math.max(100, Math.min(500, prev.networkTraffic + (Math.random() - 0.5) * 50)),
        diskUsage: Math.max(50, Math.min(95, prev.diskUsage + (Math.random() - 0.5) * 5)),
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="space-y-6">
      {/* Server Status Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-dark-card/50 backdrop-blur-sm border border-cyber-blue/30 rounded-lg p-4"
      >
        <h3 className="text-cyber-blue font-bold mb-4 flex items-center">
          <Server className="w-5 h-5 mr-2" />
          服务器状态
        </h3>
        <div className="text-center">
          <div className="text-3xl font-bold text-cyber-green">{stats.onlineServers}</div>
          <div className="text-sm text-gray-400">在线 / {stats.totalServers} 台</div>
        </div>
      </motion.div>

      {/* System Metrics */}
      <div className="grid grid-cols-1 gap-4">
        <StatusCard
          title="CPU使用率"
          value={stats.cpuUsage}
          unit="%"
          status={stats.cpuUsage > 80 ? 'error' : stats.cpuUsage > 60 ? 'warning' : 'online'}
          icon={<Cpu className="w-4 h-4" />}
          trend={stats.cpuUsage > 70 ? 'up' : 'stable'}
        />
        
        <StatusCard
          title="内存使用率"
          value={stats.memoryUsage}
          unit="%"
          status={stats.memoryUsage > 85 ? 'error' : stats.memoryUsage > 70 ? 'warning' : 'online'}
          icon={<Database className="w-4 h-4" />}
          trend={stats.memoryUsage > 75 ? 'up' : 'stable'}
        />
        
        <StatusCard
          title="网络流量"
          value={stats.networkTraffic}
          unit="MB/s"
          status="online"
          icon={<Wifi className="w-4 h-4" />}
          trend="stable"
        />
        
        <StatusCard
          title="磁盘使用率"
          value={stats.diskUsage}
          unit="%"
          status={stats.diskUsage > 90 ? 'error' : stats.diskUsage > 80 ? 'warning' : 'online'}
          icon={<HardDrive className="w-4 h-4" />}
          trend={stats.diskUsage > 85 ? 'up' : 'stable'}
        />
      </div>

      {/* Alert List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-dark-card/50 backdrop-blur-sm border border-red-400/30 rounded-lg p-4"
      >
        <h3 className="text-red-400 font-bold mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          告警信息
        </h3>
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {servers.map((server, index) => (
            <ServerListItem key={index} {...server} />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default StatusIndicators;
