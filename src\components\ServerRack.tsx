import React, { useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Box, Text, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

interface ServerProps {
  position: [number, number, number];
  status: 'online' | 'warning' | 'error';
  label: string;
}

const Server: React.FC<ServerProps> = ({ position, status, label }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  const getStatusColor = () => {
    switch (status) {
      case 'online': return '#00ff88';
      case 'warning': return '#ffaa00';
      case 'error': return '#ff0044';
      default: return '#666666';
    }
  };

  const getEmissiveColor = () => {
    switch (status) {
      case 'online': return '#004422';
      case 'warning': return '#442200';
      case 'error': return '#440022';
      default: return '#222222';
    }
  };

  return (
    <group position={position}>
      <Box
        ref={meshRef}
        args={[1, 0.3, 0.8]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={hovered ? 1.1 : 1}
      >
        <meshStandardMaterial
          color={getStatusColor()}
          emissive={getEmissiveColor()}
          emissiveIntensity={0.5}
          metalness={0.8}
          roughness={0.2}
        />
      </Box>
      
      {/* Status indicator light */}
      <Box args={[0.1, 0.1, 0.1]} position={[0.4, 0, 0.41]}>
        <meshStandardMaterial
          color={getStatusColor()}
          emissive={getStatusColor()}
          emissiveIntensity={1}
        />
      </Box>

      {/* Server label */}
      <Text
        position={[0, -0.3, 0]}
        fontSize={0.1}
        color="#00f5ff"
        anchorX="center"
        anchorY="middle"
      >
        {label}
      </Text>
    </group>
  );
};

interface RackProps {
  position: [number, number, number];
  servers: Array<{ status: 'online' | 'warning' | 'error'; label: string }>;
}

const Rack: React.FC<RackProps> = ({ position, servers }) => {
  return (
    <group position={position}>
      {/* Rack frame */}
      <Box args={[1.2, 3, 1]} position={[0, 0, 0]}>
        <meshStandardMaterial
          color="#333333"
          metalness={0.9}
          roughness={0.1}
          transparent
          opacity={0.3}
        />
      </Box>

      {/* Servers */}
      {servers.map((server, index) => (
        <Server
          key={index}
          position={[0, 1.2 - index * 0.4, 0]}
          status={server.status}
          label={server.label}
        />
      ))}
    </group>
  );
};

const ServerRack3D: React.FC = () => {
  const racks = [
    {
      position: [-3, 0, 0] as [number, number, number],
      servers: [
        { status: 'online' as const, label: 'WEB-01' },
        { status: 'online' as const, label: 'WEB-02' },
        { status: 'warning' as const, label: 'WEB-03' },
        { status: 'online' as const, label: 'WEB-04' },
        { status: 'online' as const, label: 'WEB-05' },
        { status: 'error' as const, label: 'WEB-06' },
      ]
    },
    {
      position: [0, 0, 0] as [number, number, number],
      servers: [
        { status: 'online' as const, label: 'DB-01' },
        { status: 'online' as const, label: 'DB-02' },
        { status: 'online' as const, label: 'DB-03' },
        { status: 'warning' as const, label: 'DB-04' },
        { status: 'online' as const, label: 'DB-05' },
        { status: 'online' as const, label: 'DB-06' },
      ]
    },
    {
      position: [3, 0, 0] as [number, number, number],
      servers: [
        { status: 'online' as const, label: 'API-01' },
        { status: 'online' as const, label: 'API-02' },
        { status: 'online' as const, label: 'API-03' },
        { status: 'online' as const, label: 'API-04' },
        { status: 'error' as const, label: 'API-05' },
        { status: 'online' as const, label: 'API-06' },
      ]
    },
    {
      position: [-1.5, 0, -3] as [number, number, number],
      servers: [
        { status: 'online' as const, label: 'CACHE-01' },
        { status: 'online' as const, label: 'CACHE-02' },
        { status: 'online' as const, label: 'CACHE-03' },
        { status: 'online' as const, label: 'CACHE-04' },
        { status: 'online' as const, label: 'CACHE-05' },
        { status: 'warning' as const, label: 'CACHE-06' },
      ]
    },
    {
      position: [1.5, 0, -3] as [number, number, number],
      servers: [
        { status: 'online' as const, label: 'STOR-01' },
        { status: 'online' as const, label: 'STOR-02' },
        { status: 'online' as const, label: 'STOR-03' },
        { status: 'online' as const, label: 'STOR-04' },
        { status: 'online' as const, label: 'STOR-05' },
        { status: 'online' as const, label: 'STOR-06' },
      ]
    }
  ];

  return (
    <div className="w-full h-full">
      <Canvas
        camera={{ position: [8, 6, 8], fov: 60 }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#00f5ff" />
        <pointLight position={[-10, 10, -10]} intensity={0.5} color="#00ff88" />
        <spotLight
          position={[0, 15, 0]}
          angle={0.3}
          penumbra={1}
          intensity={1}
          color="#ffffff"
        />

        {racks.map((rack, index) => (
          <Rack key={index} position={rack.position} servers={rack.servers} />
        ))}

        {/* Floor grid */}
        <gridHelper args={[20, 20, '#00f5ff', '#003344']} position={[0, -2, 0]} />

        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={20}
          maxPolarAngle={Math.PI / 2}
        />
      </Canvas>
    </div>
  );
};

export default ServerRack3D;
