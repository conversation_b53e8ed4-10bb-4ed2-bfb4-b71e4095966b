import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ServerRack3D from './components/ServerRack';
import MonitoringCharts from './components/MonitoringCharts';
import StatusIndicators from './components/StatusIndicators';
import AnimatedBackground from './components/AnimatedBackground';
import { Cloud, Shield, Zap } from 'lucide-react';

function App() {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-dark-bg cyber-grid relative">
      <AnimatedBackground />
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-dark-card/80 backdrop-blur-sm border-b border-cyber-blue/30 p-4"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Cloud className="w-8 h-8 text-cyber-blue" />
              <h1 className="text-2xl font-bold text-cyber-blue">
                基础设施和应用运维监控
              </h1>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <div className="text-right">
              <div className="text-cyber-blue font-bold">
                {currentTime.toLocaleDateString('zh-CN')} 星期三
              </div>
              <div className="text-cyber-green text-xl font-mono">
                {currentTime.toLocaleTimeString('zh-CN', { hour12: false })}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-cyber-green">
                <Shield className="w-5 h-5" />
                <span className="text-sm">安全状态正常</span>
              </div>
              <div className="flex items-center space-x-2 text-cyber-blue">
                <Zap className="w-5 h-5" />
                <span className="text-sm">系统运行正常</span>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Sidebar - Status Indicators */}
        <motion.aside
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="w-80 bg-dark-card/30 backdrop-blur-sm border-r border-cyber-blue/20 p-4 overflow-y-auto"
        >
          <StatusIndicators />
        </motion.aside>

        {/* Center - 3D Server Visualization */}
        <motion.main
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
          className="flex-1 relative"
        >
          <div className="absolute inset-0 holographic">
            <ServerRack3D />
          </div>

          {/* Floating Cloud Icon */}
          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 5, -5, 0]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-8 left-1/2 transform -translate-x-1/2 z-10"
          >
            <div className="bg-cyber-blue/20 backdrop-blur-sm rounded-full p-4 border border-cyber-blue/50 glow-effect">
              <Cloud className="w-12 h-12 text-cyber-blue" />
            </div>
          </motion.div>
        </motion.main>

        {/* Right Sidebar - Charts */}
        <motion.aside
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="w-96 bg-dark-card/30 backdrop-blur-sm border-l border-cyber-blue/20 p-4 overflow-y-auto"
        >
          <MonitoringCharts />
        </motion.aside>
      </div>
    </div>
  );
}

export default App;
