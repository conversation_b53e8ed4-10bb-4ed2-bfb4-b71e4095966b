"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),s=require("three-stdlib");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=n(e),c=a(t);const o=1e-5;const l=c.forwardRef((function({args:[e=1,t=1,r=1]=[],radius:s=.05,steps:n=1,smoothness:a=4,bevelSegments:o=4,creaseAngle:l=.4,children:d,...i},b){return c.createElement("mesh",u.default({ref:b},i),c.createElement(f,{args:[e,t,r],radius:s,steps:n,smoothness:a,bevelSegments:o,creaseAngle:l}),d)})),f=c.forwardRef((function({args:[e=1,t=1,n=1]=[],radius:a=.05,steps:l=1,smoothness:f=4,bevelSegments:d=4,creaseAngle:i=.4,...b},m){const h=c.useMemo((()=>function(e,t,s){const n=new r.Shape,a=s-o;return n.absarc(o,o,o,-Math.PI/2,-Math.PI,!0),n.absarc(o,t-2*a,o,Math.PI,Math.PI/2,!0),n.absarc(e-2*a,t-2*a,o,Math.PI/2,0,!0),n.absarc(e-2*a,o,o,0,-Math.PI/2,!0),n}(e,t,a)),[e,t,a]),p=c.useMemo((()=>({depth:n-2*a,bevelEnabled:!0,bevelSegments:2*d,steps:l,bevelSize:a-o,bevelThickness:a,curveSegments:f})),[n,a,f,d,l]),g=c.useRef(null);return c.useLayoutEffect((()=>{g.current&&(g.current.center(),s.toCreasedNormals(g.current,i))}),[h,p,i]),c.useImperativeHandle(m,(()=>g.current)),c.createElement("extrudeGeometry",u.default({ref:g,args:[h,p]},b))}));exports.RoundedBox=l,exports.RoundedBoxGeometry=f;
