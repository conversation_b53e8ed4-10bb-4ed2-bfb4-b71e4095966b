@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Orbitron', monospace;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0a0a0a;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #00f5ff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00d4ff;
}

/* Glowing effects */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00f5ff, #00ff88, #8b5cf6, #ff0080);
  border-radius: inherit;
  z-index: -1;
  filter: blur(10px);
  opacity: 0.7;
  animation: glow-rotate 3s linear infinite;
}

@keyframes glow-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Cyber grid background */
.cyber-grid {
  background-image:
    linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Holographic effect */
.holographic {
  background: linear-gradient(45deg,
    rgba(0, 245, 255, 0.1) 0%,
    rgba(0, 255, 136, 0.1) 25%,
    rgba(139, 92, 246, 0.1) 50%,
    rgba(255, 0, 128, 0.1) 75%,
    rgba(0, 245, 255, 0.1) 100%);
  background-size: 400% 400%;
  animation: holographic-shift 4s ease-in-out infinite;
}

@keyframes holographic-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
