import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Bar,

} from 'recharts';
import { motion } from 'framer-motion';

// Generate mock data
const generateTimeSeriesData = (points: number = 20) => {
  const data = [];
  const now = new Date();
  
  for (let i = points - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60000); // 1 minute intervals
    data.push({
      time: time.toLocaleTimeString('en-US', { hour12: false }),
      cpu: Math.random() * 40 + 30 + Math.sin(i * 0.5) * 20,
      memory: Math.random() * 30 + 50 + Math.cos(i * 0.3) * 15,
      network: Math.random() * 50 + 20,
    });
  }
  return data;
};

const generateServerData = () => [
  { name: 'WEB服务器', value: 85, color: '#00ff88' },
  { name: 'DB服务器', value: 72, color: '#00f5ff' },
  { name: 'API服务器', value: 68, color: '#8b5cf6' },
  { name: '缓存服务器', value: 45, color: '#ffaa00' },
  { name: '存储服务器', value: 92, color: '#ff0080' },
];

const CPUChart: React.FC = () => {
  const [data, setData] = useState(generateTimeSeriesData());

  useEffect(() => {
    const interval = setInterval(() => {
      setData(prevData => {
        const newData = [...prevData.slice(1)];
        const lastTime = new Date();
        newData.push({
          time: lastTime.toLocaleTimeString('en-US', { hour12: false }),
          cpu: Math.random() * 40 + 30 + Math.sin(Date.now() * 0.001) * 20,
          memory: Math.random() * 30 + 50 + Math.cos(Date.now() * 0.001) * 15,
          network: Math.random() * 50 + 20,
        });
        return newData;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-dark-card/50 backdrop-blur-sm border border-cyber-blue/30 rounded-lg p-4 glow-effect"
    >
      <h3 className="text-cyber-blue font-bold mb-4 text-lg">CPU使用率 TOP10</h3>
      <ResponsiveContainer width="100%" height={200}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#333" />
          <XAxis 
            dataKey="time" 
            stroke="#00f5ff"
            fontSize={10}
            tick={{ fill: '#00f5ff' }}
          />
          <YAxis 
            stroke="#00f5ff"
            fontSize={10}
            tick={{ fill: '#00f5ff' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#1a1a1a',
              border: '1px solid #00f5ff',
              borderRadius: '8px',
              color: '#fff'
            }}
          />
          <Line
            type="monotone"
            dataKey="cpu"
            stroke="#00ff88"
            strokeWidth={2}
            dot={{ fill: '#00ff88', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5, stroke: '#00ff88', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

const MemoryChart: React.FC = () => {
  const [data, setData] = useState(generateTimeSeriesData());

  useEffect(() => {
    const interval = setInterval(() => {
      setData(prevData => {
        const newData = [...prevData.slice(1)];
        const lastTime = new Date();
        newData.push({
          time: lastTime.toLocaleTimeString('en-US', { hour12: false }),
          cpu: Math.random() * 40 + 30,
          memory: Math.random() * 30 + 50 + Math.cos(Date.now() * 0.001) * 15,
          network: Math.random() * 50 + 20,
        });
        return newData;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-dark-card/50 backdrop-blur-sm border border-cyber-green/30 rounded-lg p-4 glow-effect"
    >
      <h3 className="text-cyber-green font-bold mb-4 text-lg">内存使用率 TOP10</h3>
      <ResponsiveContainer width="100%" height={200}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#333" />
          <XAxis 
            dataKey="time" 
            stroke="#00ff88"
            fontSize={10}
            tick={{ fill: '#00ff88' }}
          />
          <YAxis 
            stroke="#00ff88"
            fontSize={10}
            tick={{ fill: '#00ff88' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#1a1a1a',
              border: '1px solid #00ff88',
              borderRadius: '8px',
              color: '#fff'
            }}
          />
          <Line
            type="monotone"
            dataKey="memory"
            stroke="#00f5ff"
            strokeWidth={2}
            dot={{ fill: '#00f5ff', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5, stroke: '#00f5ff', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

const ServerStatusChart: React.FC = () => {
  const data = generateServerData();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4 }}
      className="bg-dark-card/50 backdrop-blur-sm border border-cyber-purple/30 rounded-lg p-4 glow-effect"
    >
      <h3 className="text-cyber-purple font-bold mb-4 text-lg">服务器负载情况</h3>
      <ResponsiveContainer width="100%" height={200}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#333" />
          <XAxis 
            dataKey="name" 
            stroke="#8b5cf6"
            fontSize={10}
            tick={{ fill: '#8b5cf6' }}
          />
          <YAxis 
            stroke="#8b5cf6"
            fontSize={10}
            tick={{ fill: '#8b5cf6' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#1a1a1a',
              border: '1px solid #8b5cf6',
              borderRadius: '8px',
              color: '#fff'
            }}
          />
          <Bar dataKey="value" fill="#8b5cf6" />
        </BarChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

const MonitoringCharts: React.FC = () => {
  return (
    <div className="space-y-6">
      <CPUChart />
      <MemoryChart />
      <ServerStatusChart />
    </div>
  );
};

export default MonitoringCharts;
